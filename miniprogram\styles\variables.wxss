/* 言策 AI量化投资小程序 - 样式变量定义 */
/* ================================================ */

:root {
  /* ==================== 主色调 ==================== */
  /* 专业金融蓝色系 - 体现科技感和专业性 */
  --primary-color: #1B4F72;          /* 主色 - 深蓝色 */
  --primary-light: #2E86C1;          /* 主色浅色 */
  --primary-lighter: #5DADE2;        /* 主色更浅 */
  --primary-dark: #154360;           /* 主色深色 */
  --primary-gradient: linear-gradient(135deg, #1B4F72 0%, #2E86C1 100%);

  /* 辅助色 */
  --secondary-color: #34495E;        /* 辅助色 - 深灰蓝 */
  --secondary-light: #5D6D7E;        /* 辅助色浅色 */
  --accent-color: #F39C12;           /* 强调色 - 金色 */

  /* ==================== 功能色 ==================== */
  /* 金融专用颜色 */
  --color-rise: #E74C3C;             /* 涨 - 红色 */
  --color-rise-light: #F1948A;       /* 涨 - 浅红色 */
  --color-fall: #27AE60;             /* 跌 - 绿色 */
  --color-fall-light: #82E0AA;       /* 跌 - 浅绿色 */
  
  /* 状态色 */
  --color-success: #27AE60;          /* 成功 */
  --color-warning: #F39C12;          /* 警告 */
  --color-danger: #E74C3C;           /* 危险 */
  --color-info: #3498DB;             /* 信息 */
  
  /* 状态色浅色版本 */
  --color-success-light: #D5F4E6;    /* 成功浅色背景 */
  --color-warning-light: #FCF3CD;    /* 警告浅色背景 */
  --color-danger-light: #FADBD8;     /* 危险浅色背景 */
  --color-info-light: #D6EAF8;       /* 信息浅色背景 */

  /* ==================== 文本色 ==================== */
  --text-primary: #2C3E50;           /* 主要文本 */
  --text-secondary: #5D6D7E;         /* 次要文本 */
  --text-tertiary: #85929E;          /* 三级文本 */
  --text-disabled: #BDC3C7;          /* 禁用文本 */
  --text-inverse: #FFFFFF;           /* 反色文本 */
  --text-link: #3498DB;              /* 链接文本 */
  --text-placeholder: #95A5A6;       /* 占位符文本 */

  /* ==================== 背景色 ==================== */
  --bg-primary: #FFFFFF;             /* 主背景 */
  --bg-secondary: #F8F9FA;           /* 次要背景 */
  --bg-tertiary: #ECF0F1;            /* 三级背景 */
  --bg-card: #FFFFFF;                /* 卡片背景 */
  --bg-overlay: rgba(0, 0, 0, 0.5);  /* 遮罩背景 */
  --bg-disabled: #F4F6F7;            /* 禁用背景 */
  
  /* 分割线 */
  --border-light: #E5E8E8;           /* 浅色边框 */
  --border-medium: #D5DBDB;          /* 中等边框 */
  --border-dark: #AEB6BF;            /* 深色边框 */
  --divider: #EAEDED;                /* 分割线 */

  /* ==================== 字体大小 ==================== */
  --font-size-xs: 20rpx;             /* 极小字体 */
  --font-size-sm: 24rpx;             /* 小字体 */
  --font-size-base: 28rpx;           /* 基础字体 */
  --font-size-lg: 32rpx;             /* 大字体 */
  --font-size-xl: 36rpx;             /* 超大字体 */
  --font-size-xxl: 40rpx;            /* 极大字体 */
  --font-size-title: 44rpx;          /* 标题字体 */
  --font-size-display: 48rpx;        /* 展示字体 */

  /* 字重 */
  --font-weight-light: 300;          /* 细体 */
  --font-weight-normal: 400;         /* 正常 */
  --font-weight-medium: 500;         /* 中等 */
  --font-weight-semibold: 600;       /* 半粗体 */
  --font-weight-bold: 700;           /* 粗体 */

  /* ==================== 间距 ==================== */
  --spacing-xs: 8rpx;                /* 极小间距 */
  --spacing-sm: 12rpx;               /* 小间距 */
  --spacing-base: 16rpx;             /* 基础间距 */
  --spacing-md: 20rpx;               /* 中等间距 */
  --spacing-lg: 24rpx;               /* 大间距 */
  --spacing-xl: 32rpx;               /* 超大间距 */
  --spacing-xxl: 48rpx;              /* 极大间距 */
  --spacing-xxxl: 64rpx;             /* 超级大间距 */

  /* ==================== 圆角 ==================== */
  --radius-sm: 8rpx;                 /* 小圆角 */
  --radius-base: 12rpx;              /* 基础圆角 */
  --radius-md: 16rpx;                /* 中等圆角 */
  --radius-lg: 20rpx;                /* 大圆角 */
  --radius-xl: 24rpx;                /* 超大圆角 */
  --radius-round: 50%;               /* 圆形 */
  --radius-pill: 999rpx;             /* 胶囊形 */

  /* ==================== 阴影 ==================== */
  /* 轻微阴影 */
  --shadow-light: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  
  /* 标准阴影 */
  --shadow-base: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  
  /* 明显阴影 */
  --shadow-strong: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  
  /* 卡片阴影 */
  --shadow-card: 0 2rpx 12rpx rgba(27, 79, 114, 0.08);
  
  /* 浮动阴影 */
  --shadow-float: 0 6rpx 24rpx rgba(27, 79, 114, 0.12);

  /* ==================== 层级 ==================== */
  --z-index-dropdown: 1000;          /* 下拉菜单 */
  --z-index-sticky: 1020;            /* 粘性定位 */
  --z-index-fixed: 1030;             /* 固定定位 */
  --z-index-modal-backdrop: 1040;    /* 模态框背景 */
  --z-index-modal: 1050;             /* 模态框 */
  --z-index-popover: 1060;           /* 弹出框 */
  --z-index-tooltip: 1070;           /* 工具提示 */
  --z-index-toast: 1080;             /* 消息提示 */

  /* ==================== 动画 ==================== */
  --transition-fast: 0.15s ease;     /* 快速过渡 */
  --transition-base: 0.3s ease;      /* 基础过渡 */
  --transition-slow: 0.5s ease;      /* 慢速过渡 */
  
  /* 缓动函数 */
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);

  /* ==================== 特殊效果 ==================== */
  /* 毛玻璃效果 */
  --backdrop-blur: blur(20rpx);
  
  /* 渐变背景 */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  --gradient-success: linear-gradient(135deg, var(--color-success) 0%, #58D68D 100%);
  --gradient-warning: linear-gradient(135deg, var(--color-warning) 0%, #F7DC6F 100%);
  --gradient-danger: linear-gradient(135deg, var(--color-danger) 0%, #F1948A 100%);
}

/* ==================== 深色模式支持 ==================== */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #1A1A1A;
    --bg-secondary: #2D2D2D;
    --bg-tertiary: #3A3A3A;
    --bg-card: #2D2D2D;
    
    --text-primary: #FFFFFF;
    --text-secondary: #B0B0B0;
    --text-tertiary: #808080;
    --text-disabled: #606060;
    
    --border-light: #404040;
    --border-medium: #505050;
    --border-dark: #606060;
    --divider: #404040;
  }
}
