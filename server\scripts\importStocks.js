const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const mongoose = require('mongoose');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// 导入股票模型
const Stock = require('../models/Stock');

/**
 * 解析日期字符串 (YYYYMMDD 格式) 为 Date 对象
 * @param {string} dateString - 格式为 YYYYMMDD 的日期字符串
 * @returns {Date} - Date 对象
 */
function parseListingDate(dateString) {
  if (!dateString || dateString.length !== 8) {
    throw new Error(`无效的日期格式: ${dateString}`);
  }
  
  const year = parseInt(dateString.substring(0, 4));
  const month = parseInt(dateString.substring(4, 6));
  const day = parseInt(dateString.substring(6, 8));
  
  return new Date(year, month - 1, day); // month 是 0-based
}

/**
 * 清理字符串，去除多余空格
 * @param {string} str - 待清理的字符串
 * @returns {string} - 清理后的字符串
 */
function cleanString(str) {
  return str ? str.trim().replace(/\s+/g, ' ') : '';
}

/**
 * 连接MongoDB数据库
 */
async function connectDatabase() {
  try {
    const mongoUri = process.env.MONGODB_URI;
    if (!mongoUri) {
      throw new Error('MONGODB_URI 环境变量未设置');
    }
    
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ 成功连接到MongoDB数据库');
    console.log(`📊 数据库地址: ${mongoUri}`);
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    process.exit(1);
  }
}

/**
 * 导入CSV数据到MongoDB
 * @param {string} csvFilePath - CSV文件路径
 */
async function importStockData(csvFilePath) {
  return new Promise((resolve, reject) => {
    const stocks = [];
    let lineCount = 0;
    let errorCount = 0;
    
    console.log('🔄 开始读取CSV文件...');
    
    fs.createReadStream(csvFilePath, { encoding: 'utf8' })
      .pipe(csv({
        // 手动指定列名，因为CSV可能有中文列名
        headers: ['boardType', 'stockCode', 'stockName', 'industry', 'listingDateString']
      }))
      .on('data', (row) => {
        lineCount++;
        
        try {
          // 跳过表头行
          if (lineCount === 1 && row.boardType === '板块') {
            return;
          }
          
          const stockData = {
            boardType: cleanString(row.boardType),
            stockCode: cleanString(row.stockCode),
            stockName: cleanString(row.stockName),
            industry: cleanString(row.industry),
            listingDateString: cleanString(row.listingDateString),
            listingDate: parseListingDate(cleanString(row.listingDateString)),
            isActive: true
          };
          
          // 验证必填字段
          if (!stockData.boardType || !stockData.stockCode || !stockData.stockName || !stockData.industry) {
            throw new Error(`第${lineCount}行数据不完整`);
          }
          
          stocks.push(stockData);
          
          if (lineCount % 1000 === 0) {
            console.log(`📖 已读取 ${lineCount} 行数据...`);
          }
          
        } catch (error) {
          errorCount++;
          console.warn(`⚠️  第${lineCount}行数据处理失败: ${error.message}`);
          console.warn(`   数据内容: ${JSON.stringify(row)}`);
        }
      })
      .on('end', () => {
        console.log(`✅ CSV文件读取完成`);
        console.log(`📊 总行数: ${lineCount}, 成功: ${stocks.length}, 错误: ${errorCount}`);
        resolve(stocks);
      })
      .on('error', (error) => {
        console.error('❌ 读取CSV文件失败:', error);
        reject(error);
      });
  });
}

/**
 * 批量插入数据到数据库
 * @param {Array} stocks - 股票数据数组
 */
async function insertStocksToDB(stocks) {
  try {
    console.log('🔄 开始清空现有数据...');
    await Stock.deleteMany({}); // 清空现有数据
    console.log('✅ 现有数据已清空');
    
    console.log('🔄 开始批量插入数据...');
    const batchSize = 500;
    let insertedCount = 0;
    let errorCount = 0;
    
    for (let i = 0; i < stocks.length; i += batchSize) {
      const batch = stocks.slice(i, i + batchSize);
      
      try {
        await Stock.insertMany(batch, { ordered: false }); // ordered: false 允许部分失败继续
        insertedCount += batch.length;
        console.log(`✅ 已插入 ${insertedCount}/${stocks.length} 条数据`);
      } catch (error) {
        // 处理批量插入中的错误
        if (error.writeErrors) {
          errorCount += error.writeErrors.length;
          insertedCount += batch.length - error.writeErrors.length;
          
          error.writeErrors.forEach(writeError => {
            console.warn(`⚠️  插入失败: ${writeError.errmsg}`);
          });
        } else {
          errorCount += batch.length;
          console.error(`❌ 批量插入失败:`, error.message);
        }
      }
    }
    
    console.log(`🎉 数据导入完成!`);
    console.log(`📊 成功插入: ${insertedCount} 条, 失败: ${errorCount} 条`);
    
    // 显示统计信息
    await showStatistics();
    
  } catch (error) {
    console.error('❌ 数据插入失败:', error);
    throw error;
  }
}

/**
 * 显示数据库统计信息
 */
async function showStatistics() {
  try {
    const totalCount = await Stock.countDocuments();
    const boardStats = await Stock.aggregate([
      { $group: { _id: '$boardType', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    const industryStats = await Stock.aggregate([
      { $group: { _id: '$industry', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);
    
    console.log('\n📈 数据库统计信息:');
    console.log(`总股票数量: ${totalCount}`);
    
    console.log('\n板块分布:');
    boardStats.forEach(stat => {
      console.log(`  ${stat._id}: ${stat.count} 只`);
    });
    
    console.log('\n前10大行业分布:');
    industryStats.forEach(stat => {
      console.log(`  ${stat._id}: ${stat.count} 只`);
    });
    
  } catch (error) {
    console.error('❌ 获取统计信息失败:', error);
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    console.log('🚀 开始导入沪深上市公司数据...\n');
    
    // CSV文件路径
    const csvFilePath = path.join(__dirname, '../../data_gp/沪深上市公司列表_企业信息.csv');
    
    // 检查文件是否存在
    if (!fs.existsSync(csvFilePath)) {
      throw new Error(`CSV文件不存在: ${csvFilePath}`);
    }
    
    console.log(`📁 CSV文件路径: ${csvFilePath}`);
    
    // 连接数据库
    await connectDatabase();
    
    // 读取CSV数据
    const stocks = await importStockData(csvFilePath);
    
    if (stocks.length === 0) {
      console.log('⚠️  没有有效的股票数据可导入');
      return;
    }
    
    // 插入数据库
    await insertStocksToDB(stocks);
    
    console.log('\n🎉 数据导入任务完成!');
    
  } catch (error) {
    console.error('\n❌ 导入过程中发生错误:', error.message);
    console.error(error.stack);
  } finally {
    // 关闭数据库连接
    if (mongoose.connection.readyState === 1) {
      await mongoose.connection.close();
      console.log('📴 数据库连接已关闭');
    }
  }
}

// 如果直接运行此脚本，则执行主函数
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  importStockData,
  insertStocksToDB,
  connectDatabase,
  parseListingDate,
  cleanString
};
