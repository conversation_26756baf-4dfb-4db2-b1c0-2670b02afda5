const User = require('../models/User');


/**
 * 获取当前用户信息
 * @param {String} userId 用户ID
 * @returns {Object} 用户信息
 */
const getCurrentUser = async (userId) => {
  const user = await User.findById(userId);
  
  if (!user) {
    throw { statusCode: 404, message: '用户不存在' };
  }

  // 手机号打码处理
  let maskedPhone = '';
  if (user.phone) {
    maskedPhone = user.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  }

  // 准备返回的用户数据
  return {
    id: user._id,
    nickname: user.nickname,
    avatar: user.avatar,
    phone: maskedPhone,
    gender: user.gender,
    age: user.age,
    occupation: user.occupation,
    region: user.region,
    lastLoginAt: user.lastLoginAt,
    createdAt: user.createdAt
  };
};

module.exports = { getCurrentUser };