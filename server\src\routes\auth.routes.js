const express = require('express');
const { wxLoginController } = require('../controllers/auth/wxLoginController');
const { logoutController } = require('../controllers/auth/logoutController');
const { getCurrentUserController } = require('../controllers/auth/getCurrentUserController');
const { protect } = require('../middlewares/auth');

const router = express.Router();

// 微信登录
router.post('/wx-login', wxLoginController.wxLogin);

// 退出登录
router.post('/logout', protect, logoutController.logout);

// 获取当前用户信息
router.get('/me', protect, getCurrentUserController.getCurrentUser);

module.exports = router;